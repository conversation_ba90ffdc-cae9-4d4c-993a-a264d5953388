import sqlite3
import sys
import os
from datetime import datetime

# Add parent directory to path to enable absolute imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from accounting_app.modules.basic_data import Customer, Supplier, Item, Employee
from accounting_app.modules.sales import Sale
from accounting_app.modules.purchases import Purchase
from accounting_app.modules.treasury import Cash, BankOperations, SalaryHandler
from accounting_app.database import initialize_database, get_connection

def demo_application():
    """Run a demonstration of the application with sample data"""
    print("=== Running Accounting Application Demo ===")
    
    # Initialize database
    initialize_database()
    
    # Create sample customer
    cust_id = Customer.create_customer("John Doe", "123 Main St", "555-1234")
    print(f"Created customer ID: {cust_id}")
    
    # Create sample supplier
    supp_id = Supplier.create_supplier("ABC Supplies", "456 Market St", "555-5678")
    print(f"Created supplier ID: {supp_id}")
    
    # Create sample item
    item_id = Item.create_item("Laptop", "LT-001", "unit", 800, 1200)
    print(f"Created item ID: {item_id}")
    
    # Add stock via purchase
    purchase_id = Purchase.create_purchase(supp_id)
    Purchase.add_purchase_item(purchase_id, item_id, 10, 800)
    Purchase.add_payment(purchase_id, 8000)
    print(f"Recorded purchase ID: {purchase_id} (added 10 items to stock)")
    
    # Record sample sale
    sale_id = Sale.create_sale(cust_id)
    Sale.add_sale_item(sale_id, item_id, 2, 1200)
    Sale.add_payment(sale_id, 2400)
    print(f"Recorded sale ID: {sale_id}")
    
    # Show balances
    cash_balance = Cash.get_cash_balance()
    print(f"Cash balance: ${cash_balance:.2f}")
    
    # Show inventory
    from accounting_app.modules.inventory import Inventory
    stock = Inventory.get_current_stock(item_id)
    print(f"Current stock for item {item_id}: {stock}")
    
    print("=== Demo completed successfully ===")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "cli":
        from accounting_app.cli import main
        main()
    else:
        demo_application()