import sqlite3
from datetime import datetime
from ..database import get_connection
from .basic_data import Bank

class Cash:
    TABLE_NAME = "cash"
    
    @classmethod
    def create_cash_transaction(cls, amount, transaction_type, description=None, date=None):
        """Create a new cash transaction"""
        if date is None:
            date = datetime.now().strftime("%Y-%m-%d")
            
        conn = get_connection()
        if conn:
            try:
                sql = f"""INSERT INTO {cls.TABLE_NAME} (date, amount, type, description)
                          VALUES (?, ?, ?, ?)"""
                cur = conn.cursor()
                cur.execute(sql, (date, amount, transaction_type, description))
                conn.commit()
                return cur.lastrowid
            except sqlite3.Error as e:
                print(f"Error creating cash transaction: {e}")
            finally:
                conn.close()
        return None

    @classmethod
    def get_cash_balance(cls):
        """Calculate current cash balance"""
        conn = get_connection()
        if conn:
            try:
                # Calculate total income
                cur = conn.cursor()
                cur.execute(f"SELECT SUM(amount) FROM {cls.TABLE_NAME} WHERE type = 'income'")
                income = cur.fetchone()[0] or 0
                
                # Calculate total expenses
                cur.execute(f"SELECT SUM(amount) FROM {cls.TABLE_NAME} WHERE type = 'expense'")
                expenses = cur.fetchone()[0] or 0
                
                return income - expenses
            except sqlite3.Error as e:
                print(f"Error calculating cash balance: {e}")
            finally:
                conn.close()
        return 0

class BankOperations:
    TABLE_NAME = "bank_transaction"
    
    @classmethod
    def create_bank_transaction(cls, bank_id, amount, transaction_type, description=None, date=None):
        """Create a new bank transaction"""
        if date is None:
            date = datetime.now().strftime("%Y-%m-%d")
            
        conn = get_connection()
        if conn:
            try:
                # Create transaction
                sql = f"""INSERT INTO {cls.TABLE_NAME} 
                          (bank_id, amount, type, description, date)
                          VALUES (?, ?, ?, ?, ?)"""
                cur = conn.cursor()
                cur.execute(sql, (bank_id, amount, transaction_type, description, date))
                
                # Update bank balance
                if transaction_type == "deposit":
                    update_sql = "UPDATE bank SET balance = balance + ? WHERE id = ?"
                elif transaction_type == "withdrawal":
                    update_sql = "UPDATE bank SET balance = balance - ? WHERE id = ?"
                else:  # transfer
                    # For transfers, we'll need to handle both sides separately
                    update_sql = None
                
                if update_sql:
                    cur.execute(update_sql, (amount, bank_id))
                
                conn.commit()
                return cur.lastrowid
            except sqlite3.Error as e:
                print(f"Error creating bank transaction: {e}")
                conn.rollback()
            finally:
                conn.close()
        return None

    @classmethod
    def transfer_between_banks(cls, from_bank_id, to_bank_id, amount, description=None, date=None):
        """Transfer money between two bank accounts atomically"""
        if date is None:
            date = datetime.now().strftime("%Y-%m-%d")
            
        conn = get_connection()
        if conn:
            try:
                withdrawal_desc = f"Transfer to bank {to_bank_id}: {description}"
                deposit_desc = f"Transfer from bank {from_bank_id}: {description}"
                
                # Create withdrawal record
                withdrawal_sql = f"""INSERT INTO {cls.TABLE_NAME}
                          (bank_id, amount, type, description, date)
                          VALUES (?, ?, 'withdrawal', ?, ?)"""
                cur = conn.cursor()
                cur.execute(withdrawal_sql, (from_bank_id, amount, withdrawal_desc, date))
                
                # Update source bank balance
                cur.execute("UPDATE bank SET balance = balance - ? WHERE id = ?", (amount, from_bank_id))
                
                # Create deposit record
                deposit_sql = f"""INSERT INTO {cls.TABLE_NAME}
                          (bank_id, amount, type, description, date)
                          VALUES (?, ?, 'deposit', ?, ?)"""
                cur.execute(deposit_sql, (to_bank_id, amount, deposit_desc, date))
                
                # Update target bank balance
                cur.execute("UPDATE bank SET balance = balance + ? WHERE id = ?", (amount, to_bank_id))
                
                conn.commit()
                return True
            except Exception as e:
                print(f"Error transferring between banks: {e}")
                conn.rollback()
            finally:
                conn.close()
        return False

    @classmethod
    def get_bank_balance(cls, bank_id):
        """Get current balance for a bank account"""
        conn = get_connection()
        if conn:
            try:
                cur = conn.cursor()
                cur.execute("SELECT balance FROM bank WHERE id = ?", (bank_id,))
                result = cur.fetchone()
                return result[0] if result else 0
            except sqlite3.Error as e:
                print(f"Error fetching bank balance: {e}")
            finally:
                conn.close()
        return 0

class ExpenseTracker:
    @classmethod
    def record_expense(cls, expense_id, amount, payment_method='cash', bank_id=None, description=None, date=None):
        """Record an expense transaction with payment method"""
        if date is None:
            date = datetime.now().strftime("%Y-%m-%d")
            
        conn = get_connection()
        if conn:
            try:
                cur = conn.cursor()
                # Get expense name
                cur.execute("SELECT name FROM expense WHERE id = ?", (expense_id,))
                expense_name = cur.fetchone()[0] if cur.fetchone() else f"Expense #{expense_id}"
                
                desc = f"{expense_name}: {description}" if description else expense_name
                
                # Record based on payment method
                if payment_method == 'cash':
                    # Record cash transaction
                    cur.execute("INSERT INTO cash (date, amount, type, description) VALUES (?, ?, 'expense', ?)",
                               (date, amount, desc))
                elif payment_method == 'bank' and bank_id:
                    # Record bank transaction
                    cur.execute("""INSERT INTO bank_transaction
                                (bank_id, amount, type, description, date)
                                VALUES (?, ?, 'withdrawal', ?, ?)""",
                               (bank_id, amount, desc, date))
                    # Update bank balance
                    cur.execute("UPDATE bank SET balance = balance - ? WHERE id = ?",
                               (amount, bank_id))
                else:
                    raise ValueError("Invalid payment method")
                
                conn.commit()
                return True
            except Exception as e:
                print(f"Error recording expense: {e}")
                conn.rollback()
            finally:
                conn.close()
        return False
class SalaryHandler:
    @classmethod
    def pay_salary_via_cash(cls, employee_id, amount, date=None, description=None):
        """Record salary payment via cash"""
        if date is None:
            date = datetime.now().strftime("%Y-%m-%d")
        desc = f"Salary payment for employee {employee_id}"
        if description:
            desc += f": {description}"

        conn = get_connection()
        if conn:
            try:
                cur = conn.cursor()
                # Record in salary table
                cur.execute("INSERT INTO salary (employee_id, date, amount) VALUES (?, ?, ?)", 
                           (employee_id, date, amount))
                # Record cash transaction
                cur.execute("INSERT INTO cash (date, amount, type, description) VALUES (?, ?, ?, ?)",
                           (date, amount, "expense", desc))
                conn.commit()
                return True
            except Exception as e:
                conn.rollback()
                print(f"Error processing cash salary: {e}")
            finally:
                conn.close()
        return False

    @classmethod
    def pay_salary_via_bank(cls, employee_id, bank_id, amount, date=None, description=None):
        """Record salary payment via bank transfer"""
        if date is None:
            date = datetime.now().strftime("%Y-%m-%d")
        desc = f"Salary payment for employee {employee_id}"
        if description:
            desc += f": {description}"

        conn = get_connection()
        if conn:
            try:
                cur = conn.cursor()
                # Record in salary table
                cur.execute("INSERT INTO salary (employee_id, date, amount) VALUES (?, ?, ?)", 
                           (employee_id, date, amount))
                # Record bank transaction
                cur.execute("""INSERT INTO bank_transaction 
                            (bank_id, amount, type, description, date) 
                            VALUES (?, ?, ?, ?, ?)""",
                           (bank_id, amount, "withdrawal", desc, date))
                # Update bank balance
                cur.execute("UPDATE bank SET balance = balance - ? WHERE id = ?", 
                           (amount, bank_id))
                conn.commit()
                return True
            except Exception as e:
                conn.rollback()
                print(f"Error processing bank salary: {e}")
            finally:
                conn.close()
        return False
class FinancialYearHandler:
    @classmethod
    def open_financial_year(cls, start_date, end_date):
        """Create a new financial year"""
        conn = get_connection()
        if conn:
            try:
                sql = """INSERT INTO financial_year (start_date, end_date, is_closed)
                         VALUES (?, ?, 0)"""
                cur = conn.cursor()
                cur.execute(sql, (start_date, end_date))
                conn.commit()
                return cur.lastrowid
            except Exception as e:
                print(f"Error opening financial year: {e}")
                conn.rollback()
            finally:
                conn.close()
        return None

    @classmethod
    def close_financial_year(cls, year_id):
        """Close a financial year and carry forward balances"""
        conn = get_connection()
        if conn:
            try:
                cur = conn.cursor()
                # Get all balances at closing
                cur.execute("SELECT balance FROM bank")
                bank_balances = {row[0]: row[1] for row in cur.fetchall()}
                
                cur.execute("SELECT SUM(balance) FROM customer")
                customer_balance = cur.fetchone()[0] or 0
                
                cur.execute("SELECT SUM(balance) FROM supplier")
                supplier_balance = cur.fetchone()[0] or 0
                
                cash_balance = Cash.get_cash_balance()
                
                # Update year status
                cur.execute("UPDATE financial_year SET is_closed = 1 WHERE id = ?", (year_id,))
                
                # Create opening balances for new period
                # (In real accounting, this would be more complex)
                conn.commit()
                return True
            except Exception as e:
                print(f"Error closing financial year: {e}")
                conn.rollback()
            finally:
                conn.close()
        return False

    @classmethod
    def is_year_closed(cls, date):
        """Check if a date falls in a closed financial year"""
        conn = get_connection()
        if conn:
            try:
                sql = """SELECT 1 FROM financial_year 
                         WHERE date(?) BETWEEN start_date AND end_date
                         AND is_closed = 1"""
                cur = conn.cursor()
                cur.execute(sql, (date,))
                return cur.fetchone() is not None
            except Exception as e:
                print(f"Error checking financial year status: {e}")
            finally:
                conn.close()
        return False