import sqlite3
from datetime import datetime
from ..database import create_connection

class BaseModel:
    """Base model for common database operations"""
    TABLE_NAME = None
    
    @classmethod
    def create(cls, data):
        conn = create_connection()
        if conn:
            try:
                columns = ', '.join(data.keys())
                placeholders = ', '.join(['?'] * len(data))
                sql = f"INSERT INTO {cls.TABLE_NAME} ({columns}) VALUES ({placeholders})"
                cur = conn.cursor()
                cur.execute(sql, tuple(data.values()))
                conn.commit()
                return cur.lastrowid
            except sqlite3.Error as e:
                print(f"Error creating {cls.TABLE_NAME}: {e}")
            finally:
                conn.close()
        return None
    
    @classmethod
    def update(cls, id, data):
        conn = get_connection()
        if conn:
            try:
                set_clause = ', '.join([f"{key} = ?" for key in data.keys()])
                sql = f"UPDATE {cls.TABLE_NAME} SET {set_clause} WHERE id = ?"
                values = tuple(data.values()) + (id,)
                cur = conn.cursor()
                cur.execute(sql, values)
                conn.commit()
                return cur.rowcount
            except sqlite3.Error as e:
                print(f"Error updating {cls.TABLE_NAME}: {e}")
            finally:
                conn.close()
        return None
    
    @classmethod
    def delete(cls, id):
        conn = get_connection()
        if conn:
            try:
                sql = f"DELETE FROM {cls.TABLE_NAME} WHERE id = ?"
                cur = conn.cursor()
                cur.execute(sql, (id,))
                conn.commit()
                return cur.rowcount
            except sqlite3.Error as e:
                print(f"Error deleting {cls.TABLE_NAME}: {e}")
            finally:
                conn.close()
        return None
    
    @classmethod
    def get_all(cls):
        conn = get_connection()
        if conn:
            try:
                cur = conn.cursor()
                cur.execute(f"SELECT * FROM {cls.TABLE_NAME}")
                rows = cur.fetchall()
                return rows
            except sqlite3.Error as e:
                print(f"Error fetching {cls.TABLE_NAME}: {e}")
            finally:
                conn.close()
        return []
    
    @classmethod
    def get_by_id(cls, id):
        conn = get_connection()
        if conn:
            try:
                cur = conn.cursor()
                cur.execute(f"SELECT * FROM {cls.TABLE_NAME} WHERE id = ?", (id,))
                row = cur.fetchone()
                return row
            except sqlite3.Error as e:
                print(f"Error fetching {cls.TABLE_NAME}: {e}")
            finally:
                conn.close()
        return None

class Company(BaseModel):
    TABLE_NAME = "company"
    
    @classmethod
    def create_company(cls, name, address=None, phone=None, tax_id=None):
        data = {
            "name": name,
            "address": address,
            "phone": phone,
            "tax_id": tax_id
        }
        return cls.create(data)

class Customer(BaseModel):
    TABLE_NAME = "customer"
    
    @classmethod
    def create_customer(cls, name, address=None, phone=None, balance=0):
        data = {
            "name": name,
            "address": address,
            "phone": phone,
            "balance": balance
        }
        return cls.create(data)

class Supplier(BaseModel):
    TABLE_NAME = "supplier"
    
    @classmethod
    def create_supplier(cls, name, address=None, phone=None, balance=0):
        data = {
            "name": name,
            "address": address,
            "phone": phone,
            "balance": balance
        }
        return cls.create(data)

class Warehouse(BaseModel):
    TABLE_NAME = "warehouse"
    
    @classmethod
    def create_warehouse(cls, name, location=None):
        data = {
            "name": name,
            "location": location
        }
        return cls.create(data)

class Item(BaseModel):
    TABLE_NAME = "item"
    
    @classmethod
    def create_item(cls, name, code=None, unit=None, purchase_price=0, sale_price=0, warehouse_id=None):
        data = {
            "name": name,
            "code": code,
            "unit": unit,
            "purchase_price": purchase_price,
            "sale_price": sale_price,
            "warehouse_id": warehouse_id
        }
        return cls.create(data)

class Bank(BaseModel):
    TABLE_NAME = "bank"
    
    @classmethod
    def create_bank(cls, name, account_number=None, balance=0):
        data = {
            "name": name,
            "account_number": account_number,
            "balance": balance
        }
        return cls.create(data)

class Expense(BaseModel):
    TABLE_NAME = "expense"
    
    @classmethod
    def create_expense(cls, name, description=None):
        data = {
            "name": name,
            "description": description
        }
        return cls.create(data)

class User(BaseModel):
    TABLE_NAME = "user"
    
    @classmethod
    def create_user(cls, username, password, role=None):
        data = {
            "username": username,
            "password": password,
            "role": role
        }
        return cls.create(data)

class Employee(BaseModel):
    TABLE_NAME = "employee"
    
    @classmethod
    def create_employee(cls, name, position=None, salary=0, hire_date=None):
        if hire_date is None:
            hire_date = datetime.now().strftime("%Y-%m-%d")
        data = {
            "name": name,
            "position": position,
            "salary": salary,
            "hire_date": hire_date
        }
        return cls.create(data)

class Custody(BaseModel):
    TABLE_NAME = "custody"
    
    @classmethod
    def create_custody(cls, employee_id, item_id, quantity, date=None):
        if date is None:
            date = datetime.now().strftime("%Y-%m-%d")
        data = {
            "employee_id": employee_id,
            "item_id": item_id,
            "quantity": quantity,
            "date": date
        }
        return cls.create(data)
