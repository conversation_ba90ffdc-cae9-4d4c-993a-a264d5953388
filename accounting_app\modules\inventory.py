import sqlite3
from datetime import datetime
from ..database import get_connection
from .basic_data import Item, Warehouse

class Inventory:
    TABLE_NAME = "inventory"
    
    @classmethod
    def create_inventory_record(cls, item_id, quantity, date=None):
        """Create a new inventory record"""
        if date is None:
            date = datetime.now().strftime("%Y-%m-%d")

        conn = get_connection()
        if conn:
            try:
                sql = f"""INSERT INTO {cls.TABLE_NAME} (item_id, quantity, date)
                          VALUES (?, ?, ?)"""
                cur = conn.cursor()
                cur.execute(sql, (item_id, quantity, date))
                conn.commit()
                return cur.lastrowid
            except sqlite3.Error as e:
                print(f"Error creating inventory record: {e}")
            finally:
                conn.close()
        return None

    @classmethod
    def update_inventory_record(cls, record_id, quantity=None, date=None):
        """Update an existing inventory record"""
        conn = get_connection()
        if conn:
            try:
                updates = []
                params = []
                
                if quantity is not None:
                    updates.append("quantity = ?")
                    params.append(quantity)
                if date is not None:
                    updates.append("date = ?")
                    params.append(date)
                    
                if not updates:
                    return 0
                    
                params.append(record_id)
                sql = f"UPDATE {cls.TABLE_NAME} SET {', '.join(updates)} WHERE id = ?"
                cur = conn.cursor()
                cur.execute(sql, tuple(params))
                conn.commit()
                return cur.rowcount
            except sqlite3.Error as e:
                print(f"Error updating inventory record: {e}")
            finally:
                conn.close()
        return None

    @classmethod
    def delete_inventory_record(cls, record_id):
        """Delete an inventory record"""
        conn = get_connection()
        if conn:
            try:
                sql = f"DELETE FROM {cls.TABLE_NAME} WHERE id = ?"
                cur = conn.cursor()
                cur.execute(sql, (record_id,))
                conn.commit()
                return cur.rowcount
            except sqlite3.Error as e:
                print(f"Error deleting inventory record: {e}")
            finally:
                conn.close()
        return None

    @classmethod
    def get_inventory_record(cls, record_id):
        """Get a specific inventory record by ID"""
        conn = get_connection()
        if conn:
            try:
                sql = f"SELECT * FROM {cls.TABLE_NAME} WHERE id = ?"
                cur = conn.cursor()
                cur.execute(sql, (record_id,))
                return cur.fetchone()
            except sqlite3.Error as e:
                print(f"Error fetching inventory record: {e}")
            finally:
                conn.close()
        return None

    @classmethod
    def get_all_inventory_records(cls):
        """Get all inventory records"""
        conn = get_connection()
        if conn:
            try:
                sql = f"SELECT * FROM {cls.TABLE_NAME}"
                cur = conn.cursor()
                cur.execute(sql)
                return cur.fetchall()
            except sqlite3.Error as e:
                print(f"Error fetching inventory records: {e}")
            finally:
                conn.close()
        return []

    @classmethod
    def get_current_stock(cls, item_id):
        """Calculate current stock level for an item"""
        conn = get_connection()
        if conn:
            try:
                sql = f"SELECT SUM(quantity) FROM {cls.TABLE_NAME} WHERE item_id = ?"
                cur = conn.cursor()
                cur.execute(sql, (item_id,))
                result = cur.fetchone()
                return result[0] if result[0] is not None else 0
            except sqlite3.Error as e:
                print(f"Error calculating current stock: {e}")
            finally:
                conn.close()
        return 0

    @classmethod
    def adjust_stock(cls, item_id, quantity_change, date=None):
        """Adjust stock level for an item by updating existing record or creating new one"""
        if date is None:
            date = datetime.now().strftime("%Y-%m-%d")
            
        # Try to find existing record for today
        conn = get_connection()
        if conn:
            try:
                cur = conn.cursor()
                cur.execute("SELECT id, quantity FROM inventory WHERE item_id = ? AND date = ?",
                           (item_id, date))
                record = cur.fetchone()
                
                if record:
                    # Update existing record
                    new_quantity = record[1] + quantity_change
                    cur.execute("UPDATE inventory SET quantity = ? WHERE id = ?",
                               (new_quantity, record[0]))
                else:
                    # Create new record
                    cur.execute("INSERT INTO inventory (item_id, quantity, date) VALUES (?, ?, ?)",
                               (item_id, quantity_change, date))
                
                conn.commit()
                return True
            except sqlite3.Error as e:
                print(f"Error adjusting stock: {e}")
                conn.rollback()
                return False
            finally:
                conn.close()
        return False