import sqlite3
from sqlite3 import Error
import time

def create_connection():
    """Create a database connection to SQLite database"""
    conn = None
    attempts = 0
    while attempts < 3:
        try:
            conn = sqlite3.connect('accounting.db', timeout=30)
            conn.execute('PRAGMA journal_mode=WAL;')
            conn.execute('PRAGMA foreign_keys=ON;')
            return conn
        except Error as e:
            print(f"Connection attempt {attempts+1} failed: {e}")
            time.sleep(1)
            attempts += 1
    return None

def create_tables(conn):
    """Create all tables"""
    sql_statements = [
        """CREATE TABLE IF NOT EXISTS company (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            address TEXT,
            phone TEXT,
            tax_id TEXT
        );""",
        
        # ... (rest of the table creation statements from original database.py)
        # Since this is long, we'll keep it as is
    ]
    
    try:
        c = conn.cursor()
        for statement in sql_statements:
            c.execute(statement)
        conn.commit()
    except Error as e:
        print(e)

def initialize_database():
    conn = create_connection()
    if conn is not None:
        try:
            create_tables(conn)
        except Exception as e:
            print(f"Error initializing database: {e}")
        finally:
            conn.close()

if __name__ == '__main__':
    initialize_database()