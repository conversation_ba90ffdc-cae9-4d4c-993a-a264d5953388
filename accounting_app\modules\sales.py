import sqlite3
from datetime import datetime
from ..database import get_connection
from .basic_data import Customer, Item
from .inventory import Inventory

class Sale:
    TABLE_NAME = "sale"
    ITEM_TABLE_NAME = "sale_item"
    
    @classmethod
    def create_sale(cls, customer_id, date=None):
        """Create a new sale transaction"""
        if date is None:
            date = datetime.now().strftime("%Y-%m-%d")
            
        conn = get_connection()
        if conn:
            try:
                # Create sale record
                sql = f"""INSERT INTO {cls.TABLE_NAME} (customer_id, date, total, paid, remaining)
                          VALUES (?, ?, 0, 0, 0)"""
                cur = conn.cursor()
                cur.execute(sql, (customer_id, date))
                sale_id = cur.lastrowid
                conn.commit()
                return sale_id
            except sqlite3.Error as e:
                print(f"Error creating sale: {e}")
                conn.rollback()
            finally:
                conn.close()
        return None

    @classmethod
    def add_sale_item(cls, sale_id, item_id, quantity, price):
        """Add an item to a sale"""
        # Validate input
        if quantity <= 0:
            print("Error: Quantity must be positive")
            return None
        if price <= 0:
            print("Error: Price must be positive")
            return None
            
        conn = get_connection()
        if conn:
            try:
                # First verify the sale exists
                cur = conn.cursor()
                cur.execute(f"SELECT id FROM {cls.TABLE_NAME} WHERE id = ?", (sale_id,))
                if not cur.fetchone():
                    print(f"Error: Sale ID {sale_id} does not exist")
                    return None
                
                # Verify item exists and has sufficient stock
                from .inventory import Inventory
                current_stock = Inventory.get_current_stock(item_id)
                if current_stock < quantity:
                    print(f"Error: Insufficient stock for item {item_id} (available: {current_stock}, requested: {quantity})")
                    return None
                
                # Add sale item
                sql = f"""INSERT INTO {cls.ITEM_TABLE_NAME} (sale_id, item_id, quantity, price)
                          VALUES (?, ?, ?, ?)"""
                cur.execute(sql, (sale_id, item_id, quantity, price))
                
                # Update sale total
                item_total = quantity * price
                cur.execute(f"UPDATE {cls.TABLE_NAME} SET total = total + ? WHERE id = ?",
                           (item_total, sale_id))
                
                # Update remaining balance
                cur.execute(f"UPDATE {cls.TABLE_NAME} SET remaining = total - paid WHERE id = ?",
                           (sale_id,))
                
                # Update inventory
                Inventory.adjust_stock(item_id, -quantity)
                
                conn.commit()
                return cur.lastrowid
            except sqlite3.Error as e:
                print(f"Error adding sale item: {e}")
                conn.rollback()
            finally:
                conn.close()
        return None

    @classmethod
    def add_payment(cls, sale_id, amount, payment_date=None):
        """Add payment to a sale"""
        if amount <= 0:
            print("Error: Payment amount must be positive")
            return False
            
        if payment_date is None:
            payment_date = datetime.now().strftime("%Y-%m-%d")
            
        conn = get_connection()
        if conn:
            try:
                # Get current sale details
                cur = conn.cursor()
                cur.execute(f"SELECT total, paid, remaining FROM {cls.TABLE_NAME} WHERE id = ?", (sale_id,))
                total, paid, remaining = cur.fetchone()
                
                # Validate payment amount
                if amount > remaining:
                    print(f"Error: Payment amount (${amount}) exceeds remaining balance (${remaining})")
                    return False
                
                # Update paid amount
                cur.execute(f"UPDATE {cls.TABLE_NAME} SET paid = paid + ? WHERE id = ?", 
                           (amount, sale_id))
                
                # Update remaining balance
                cur.execute(f"UPDATE {cls.TABLE_NAME} SET remaining = total - paid WHERE id = ?", 
                           (sale_id,))
                
                # Update customer balance
                cur.execute("SELECT customer_id FROM sale WHERE id = ?", (sale_id,))
                customer_id = cur.fetchone()[0]
                cur.execute("UPDATE customer SET balance = balance - ? WHERE id = ?", 
                           (amount, customer_id))
                
                # Record cash transaction
                from .treasury import Cash  # Avoid circular import
                Cash.create_cash_transaction(amount, "income", f"Payment for sale #{sale_id}", payment_date)
                
                conn.commit()
                return True
            except sqlite3.Error as e:
                print(f"Error adding payment: {e}")
                conn.rollback()
            finally:
                conn.close()
        return False

    @classmethod
    def create_return(cls, sale_id, return_date=None):
        """Create a return for a sale"""
        if return_date is None:
            return_date = datetime.now().strftime("%Y-%m-%d")
            
        conn = get_connection()
        if conn:
            try:
                # Get original sale items
                cur = conn.cursor()
                cur.execute(f"SELECT * FROM {cls.ITEM_TABLE_NAME} WHERE sale_id = ?", (sale_id,))
                items = cur.fetchall()
                
                # Create return sale (negative sale)
                return_sale_id = cls.create_sale(
                    cur.execute("SELECT customer_id FROM sale WHERE id = ?", (sale_id,)).fetchone()[0],
                    return_date
                )
                
                # Add returned items
                for item in items:
                    item_id, quantity, price = item[3], item[4], item[5]
                    cls.add_sale_item(return_sale_id, item_id, quantity, price)
                    # Note: This will add inventory back and create negative sale
                
                # Update return sale to negative values
                cur.execute(f"UPDATE {cls.TABLE_NAME} SET total = -total, paid = -paid, remaining = -remaining WHERE id = ?", 
                           (return_sale_id,))
                
                conn.commit()
                return return_sale_id
            except sqlite3.Error as e:
                print(f"Error creating sale return: {e}")
                conn.rollback()
            finally:
                conn.close()
        return None

    @classmethod
    def get_sale_details(cls, sale_id):
        """Get sale details with items"""
        conn = get_connection()
        if conn:
            try:
                # Get sale header
                cur = conn.cursor()
                cur.execute(f"SELECT * FROM {cls.TABLE_NAME} WHERE id = ?", (sale_id,))
                sale = cur.fetchone()
                
                if not sale:
                    return None
                
                # Get sale items
                cur.execute(f"""SELECT i.name, si.quantity, si.price 
                               FROM {cls.ITEM_TABLE_NAME} si
                               JOIN item i ON si.item_id = i.id
                               WHERE si.sale_id = ?""", (sale_id,))
                items = cur.fetchall()
                
                return {
                    "sale": sale,
                    "items": items
                }
            except sqlite3.Error as e:
                print(f"Error fetching sale details: {e}")
            finally:
                conn.close()
        return None