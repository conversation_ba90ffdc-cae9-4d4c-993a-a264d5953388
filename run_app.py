#!/usr/bin/env python3
"""
تطبيق المحاسبة - ملف التشغيل الرئيسي
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from accounting_app.database import initialize_database
from accounting_app.modules.basic_data import Customer, Supplier, Item
from accounting_app.modules.sales import Sale
from accounting_app.modules.purchases import Purchase
from accounting_app.modules.treasury import Cash
from accounting_app.modules.inventory import Inventory

def print_menu():
    print("\n" + "="*50)
    print("🏢 نظام المحاسبة - القائمة الرئيسية")
    print("="*50)
    print("1. تشغيل العرض التوضيحي")
    print("2. إنشاء عميل جديد")
    print("3. إنشاء مورد جديد") 
    print("4. إنشاء صنف جديد")
    print("5. إنشاء فاتورة بيع")
    print("6. إنشاء فاتورة شراء")
    print("7. عرض رصيد الخزينة")
    print("8. عرض المخزون")
    print("9. خروج")
    print("="*50)

def run_demo():
    """تشغيل العرض التوضيحي"""
    print("\n🚀 تشغيل العرض التوضيحي...")
    print("="*40)
    
    # تهيئة قاعدة البيانات
    initialize_database()
    
    # إنشاء عميل تجريبي
    customer_id = Customer.create_customer("أحمد محمد", "الرياض، السعودية", "**********")
    print(f"✅ تم إنشاء العميل رقم: {customer_id}")
    
    # إنشاء مورد تجريبي
    supplier_id = Supplier.create_supplier("شركة التوريدات المتقدمة", "جدة، السعودية", "0509876543")
    print(f"✅ تم إنشاء المورد رقم: {supplier_id}")
    
    # إنشاء صنف تجريبي
    item_id = Item.create_item("لابتوب ديل", "DELL-001", "قطعة", 2000, 3000)
    print(f"✅ تم إنشاء الصنف رقم: {item_id}")
    
    print("\n📊 العرض التوضيحي اكتمل بنجاح!")
    print("="*40)

def create_customer():
    """إنشاء عميل جديد"""
    print("\n👤 إنشاء عميل جديد")
    print("-" * 30)
    name = input("اسم العميل: ")
    address = input("العنوان: ")
    phone = input("رقم الهاتف: ")
    
    customer_id = Customer.create_customer(name, address, phone)
    if customer_id:
        print(f"✅ تم إنشاء العميل بنجاح! رقم العميل: {customer_id}")
    else:
        print("❌ فشل في إنشاء العميل")

def create_supplier():
    """إنشاء مورد جديد"""
    print("\n🏭 إنشاء مورد جديد")
    print("-" * 30)
    name = input("اسم المورد: ")
    address = input("العنوان: ")
    phone = input("رقم الهاتف: ")
    
    supplier_id = Supplier.create_supplier(name, address, phone)
    if supplier_id:
        print(f"✅ تم إنشاء المورد بنجاح! رقم المورد: {supplier_id}")
    else:
        print("❌ فشل في إنشاء المورد")

def create_item():
    """إنشاء صنف جديد"""
    print("\n📦 إنشاء صنف جديد")
    print("-" * 30)
    name = input("اسم الصنف: ")
    code = input("كود الصنف: ")
    unit = input("الوحدة: ")
    try:
        purchase_price = float(input("سعر الشراء: "))
        sale_price = float(input("سعر البيع: "))
    except ValueError:
        print("❌ يرجى إدخال أرقام صحيحة للأسعار")
        return
    
    item_id = Item.create_item(name, code, unit, purchase_price, sale_price)
    if item_id:
        print(f"✅ تم إنشاء الصنف بنجاح! رقم الصنف: {item_id}")
    else:
        print("❌ فشل في إنشاء الصنف")

def view_cash_balance():
    """عرض رصيد الخزينة"""
    print("\n💰 رصيد الخزينة")
    print("-" * 30)
    balance = Cash.get_cash_balance()
    print(f"الرصيد الحالي: {balance:.2f} ريال")

def view_inventory():
    """عرض المخزون"""
    print("\n📊 تقرير المخزون")
    print("-" * 30)
    items = Item.get_all()
    if items:
        print(f"{'الرقم':<5} {'الاسم':<20} {'الكود':<10} {'المخزون':<10}")
        print("-" * 50)
        for item in items:
            stock = Inventory.get_current_stock(item[0])
            print(f"{item[0]:<5} {item[1]:<20} {item[2] or 'غير محدد':<10} {stock:<10}")
    else:
        print("لا توجد أصناف في النظام")

def main():
    """الدالة الرئيسية"""
    print("🎉 مرحباً بك في نظام المحاسبة!")
    
    # تهيئة قاعدة البيانات
    initialize_database()
    
    while True:
        try:
            print_menu()
            choice = input("\nاختر رقم العملية: ").strip()
            
            if choice == '1':
                run_demo()
            elif choice == '2':
                create_customer()
            elif choice == '3':
                create_supplier()
            elif choice == '4':
                create_item()
            elif choice == '5':
                print("🚧 إنشاء فاتورة البيع قيد التطوير...")
            elif choice == '6':
                print("🚧 إنشاء فاتورة الشراء قيد التطوير...")
            elif choice == '7':
                view_cash_balance()
            elif choice == '8':
                view_inventory()
            elif choice == '9':
                print("👋 شكراً لاستخدام نظام المحاسبة!")
                break
            else:
                print("❌ اختيار غير صحيح، يرجى المحاولة مرة أخرى")
                
        except KeyboardInterrupt:
            print("\n\n👋 تم إنهاء البرنامج بواسطة المستخدم")
            break
        except Exception as e:
            print(f"❌ حدث خطأ: {e}")

if __name__ == "__main__":
    main()
