import sys
import os
from datetime import datetime

# Add parent directory to path to enable absolute imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from accounting_app.database import initialize_database
from accounting_app.modules.basic_data import Customer, Supplier, Item
from accounting_app.modules.sales import Sale
from accounting_app.modules.purchases import Purchase
from accounting_app.modules.treasury import Cash, BankOperations
from accounting_app.modules.inventory import Inventory

def print_menu():
    print("\n==== Accounting System CLI ====")
    print("1. Run Demo")
    print("2. Create Customer")
    print("3. Create Supplier")
    print("4. Create Item")
    print("5. Create Sale")
    print("6. Create Purchase")
    print("7. View Cash Balance")
    print("8. View Inventory")
    print("9. Exit")
    print("===============================")

def run_demo():
    from .main import demo_application
    demo_application()

def create_customer():
    name = input("Customer name: ")
    address = input("Address: ")
    phone = input("Phone: ")
    customer_id = Customer.create_customer(name, address, phone)
    print(f"Created customer ID: {customer_id}")

def create_supplier():
    name = input("Supplier name: ")
    address = input("Address: ")
    phone = input("Phone: ")
    supplier_id = Supplier.create_supplier(name, address, phone)
    print(f"Created supplier ID: {supplier_id}")

def create_item():
    name = input("Item name: ")
    code = input("Item code: ")
    unit = input("Unit: ")
    purchase_price = float(input("Purchase price: "))
    sale_price = float(input("Sale price: "))
    item_id = Item.create_item(name, code, unit, purchase_price, sale_price)
    print(f"Created item ID: {item_id}")

def create_sale():
    customer_id = int(input("Customer ID: "))
    sale_id = Sale.create_sale(customer_id)
    print(f"Created sale ID: {sale_id}")
    
    while True:
        item_id = int(input("Item ID (0 to finish): "))
        if item_id == 0:
            break
        quantity = int(input("Quantity: "))
        price = float(input("Price: "))
        Sale.add_sale_item(sale_id, item_id, quantity, price)
        
    amount = float(input("Payment amount: "))
    Sale.add_payment(sale_id, amount)
    print(f"Sale {sale_id} completed")

def create_purchase():
    supplier_id = int(input("Supplier ID: "))
    purchase_id = Purchase.create_purchase(supplier_id)
    print(f"Created purchase ID: {purchase_id}")
    
    while True:
        item_id = int(input("Item ID (0 to finish): "))
        if item_id == 0:
            break
        quantity = int(input("Quantity: "))
        price = float(input("Price: "))
        Purchase.add_purchase_item(purchase_id, item_id, quantity, price)
        
    amount = float(input("Payment amount: "))
    Purchase.add_payment(purchase_id, amount)
    print(f"Purchase {purchase_id} completed")

def view_cash_balance():
    balance = Cash.get_cash_balance()
    print(f"Current cash balance: ${balance:.2f}")

def view_inventory():
    print("\nInventory Items:")
    items = Item.get_all()
    for item in items:
        stock = Inventory.get_current_stock(item[0])
        print(f"ID: {item[0]}, Name: {item[1]}, Stock: {stock}")

def main():
    initialize_database()
    
    try:
        while True:
            print_menu()
            try:
                choice = input("Enter choice: ")
            except EOFError:
                print("\nExiting...")
                break
            except KeyboardInterrupt:
                print("\nExiting...")
                break
                
            try:
                if choice == '1':
                    run_demo()
                elif choice == '2':
                    create_customer()
                elif choice == '3':
                    create_supplier()
                elif choice == '4':
                    create_item()
                elif choice == '5':
                    create_sale()
                elif choice == '6':
                    create_purchase()
                elif choice == '7':
                    view_cash_balance()
                elif choice == '8':
                    view_inventory()
                elif choice == '9':
                    print("Exiting...")
                    sys.exit(0)
                else:
                    print("Invalid choice")
            except Exception as e:
                print(f"Error: {e}")
    except SystemExit:
        pass

if __name__ == "__main__":
    main()