import sqlite3
from datetime import datetime
from ..database import create_connection
from .basic_data import Supplier, Item
from .inventory import Inventory

class Purchase:
    TABLE_NAME = "purchase"
    ITEM_TABLE_NAME = "purchase_item"
    
    @classmethod
    def create_purchase(cls, supplier_id, date=None):
        """Create a new purchase transaction"""
        if date is None:
            date = datetime.now().strftime("%Y-%m-%d")
            
        conn = create_connection()
        if conn:
            try:
                # Create purchase record
                sql = f"""INSERT INTO {cls.TABLE_NAME} (supplier_id, date, total, paid, remaining)
                          VALUES (?, ?, 0, 0, 0)"""
                cur = conn.cursor()
                cur.execute(sql, (supplier_id, date))
                purchase_id = cur.lastrowid
                conn.commit()
                return purchase_id
            except sqlite3.Error as e:
                print(f"Error creating purchase: {e}")
                conn.rollback()
            finally:
                conn.close()
        return None

    @classmethod
    def add_purchase_item(cls, purchase_id, item_id, quantity, price):
        """Add an item to a purchase"""
        # Validate input
        if quantity <= 0:
            print("Error: Quantity must be positive")
            return None
        if price <= 0:
            print("Error: Price must be positive")
            return None
            
        conn = create_connection()
        if conn:
            try:
                # Verify purchase exists
                cur = conn.cursor()
                cur.execute(f"SELECT id FROM {cls.TABLE_NAME} WHERE id = ?", (purchase_id,))
                if not cur.fetchone():
                    print(f"Error: Purchase ID {purchase_id} does not exist")
                    return None
                
                # Add purchase item
                sql = f"""INSERT INTO {cls.ITEM_TABLE_NAME} (purchase_id, item_id, quantity, price)
                          VALUES (?, ?, ?, ?)"""
                cur.execute(sql, (purchase_id, item_id, quantity, price))
                
                # Update purchase total
                item_total = quantity * price
                cur.execute(f"UPDATE {cls.TABLE_NAME} SET total = total + ? WHERE id = ?",
                           (item_total, purchase_id))
                
                # Update remaining balance
                cur.execute(f"UPDATE {cls.TABLE_NAME} SET remaining = total - paid WHERE id = ?",
                           (purchase_id,))
                
                # Update inventory
                Inventory.adjust_stock(item_id, quantity)
                
                conn.commit()
                return cur.lastrowid
            except sqlite3.Error as e:
                print(f"Error adding purchase item: {e}")
                conn.rollback()
            finally:
                conn.close()
        return None

    @classmethod
    def add_payment(cls, purchase_id, amount, payment_date=None):
        """Add payment to a purchase"""
        if amount <= 0:
            print("Error: Payment amount must be positive")
            return False
            
        if payment_date is None:
            payment_date = datetime.now().strftime("%Y-%m-%d")
            
        conn = create_connection()
        if conn:
            try:
                # Get current purchase details
                cur = conn.cursor()
                cur.execute(f"SELECT total, paid, remaining FROM {cls.TABLE_NAME} WHERE id = ?", (purchase_id,))
                total, paid, remaining = cur.fetchone()
                
                # Validate payment amount
                if amount > remaining:
                    print(f"Error: Payment amount (${amount}) exceeds remaining balance (${remaining})")
                    return False
                
                # Update paid amount
                cur.execute(f"UPDATE {cls.TABLE_NAME} SET paid = paid + ? WHERE id = ?",
                           (amount, purchase_id))
                
                # Update remaining balance
                cur.execute(f"UPDATE {cls.TABLE_NAME} SET remaining = total - paid WHERE id = ?",
                           (purchase_id,))
                
                # Update supplier balance
                cur.execute("SELECT supplier_id FROM purchase WHERE id = ?", (purchase_id,))
                supplier_id = cur.fetchone()[0]
                cur.execute("UPDATE supplier SET balance = balance - ? WHERE id = ?",
                           (amount, supplier_id))
                
                # Record cash transaction
                from .treasury import Cash  # Avoid circular import
                Cash.create_cash_transaction(amount, "expense", f"Payment for purchase #{purchase_id}", payment_date)
                
                conn.commit()
                return True
            except sqlite3.Error as e:
                print(f"Error adding payment: {e}")
                conn.rollback()
            finally:
                conn.close()
        return False

    @classmethod
    def create_return(cls, purchase_id, return_date=None):
        """Create a return for a purchase"""
        if return_date is None:
            return_date = datetime.now().strftime("%Y-%m-%d")
            
        conn = get_connection()
        if conn:
            try:
                # Get original purchase items
                cur = conn.cursor()
                cur.execute(f"SELECT * FROM {cls.ITEM_TABLE_NAME} WHERE purchase_id = ?", (purchase_id,))
                items = cur.fetchall()
                
                # Create return purchase (negative purchase)
                return_purchase_id = cls.create_purchase(
                    cur.execute("SELECT supplier_id FROM purchase WHERE id = ?", (purchase_id,)).fetchone()[0],
                    return_date
                )
                
                # Add returned items
                for item in items:
                    item_id, quantity, price = item[3], item[4], item[5]
                    cls.add_purchase_item(return_purchase_id, item_id, quantity, price)
                    # Note: This will remove inventory and create negative purchase
                
                # Update return purchase to negative values
                cur.execute(f"UPDATE {cls.TABLE_NAME} SET total = -total, paid = -paid, remaining = -remaining WHERE id = ?", 
                           (return_purchase_id,))
                
                conn.commit()
                return return_purchase_id
            except sqlite3.Error as e:
                print(f"Error creating purchase return: {e}")
                conn.rollback()
            finally:
                conn.close()
        return None

    @classmethod
    def get_purchase_details(cls, purchase_id):
        """Get purchase details with items"""
        conn = create_connection()
        if conn:
            try:
                # Get purchase header
                cur = conn.cursor()
                cur.execute(f"SELECT * FROM {cls.TABLE_NAME} WHERE id = ?", (purchase_id,))
                purchase = cur.fetchone()
                
                if not purchase:
                    return None
                
                # Get purchase items
                cur.execute(f"""SELECT i.name, pi.quantity, pi.price 
                               FROM {cls.ITEM_TABLE_NAME} pi
                               JOIN item i ON pi.item_id = i.id
                               WHERE pi.purchase_id = ?""", (purchase_id,))
                items = cur.fetchall()
                
                return {
                    "purchase": purchase,
                    "items": items
                }
            except sqlite3.Error as e:
                print(f"Error fetching purchase details: {e}")
            finally:
                conn.close()
        return None